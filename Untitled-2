The performance test of the testing pipeline is failing!


  1) [chromium] › src/__tests__/e2e/admin-workflows.spec.ts:193:3 › Admin Workflow E2E Tests › Super Admin Workflow 

    TimeoutError: page.waitForSelector: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('text=Magic link sent! Please check your email') to be visible


       at auth-helpers.ts:197

      195 |
      196 |   // Wait for the magic link sent message
    > 197 |   await page.waitForSelector('text=Magic link sent! Please check your email', { timeout: 10000 })
          |              ^
      198 |
      199 |   // Now simulate clicking the magic link by navigating to the magic link page
      200 |   await page.goto(`/auth/magic-link#${token}`)
        at signInSuperAdmin (/home/<USER>/work/workwell/workwell/src/__tests__/e2e/auth-helpers.ts:197:14)
        at /home/<USER>/work/workwell/workwell/src/__tests__/e2e/admin-workflows.spec.ts:195:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/admin-workflows-Admin-Work-9253e--Tests-Super-Admin-Workflow-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: test-results/admin-workflows-Admin-Work-9253e--Tests-Super-Admin-Workflow-chromium/error-context.md

    Retry #1 ───────────────────────────────────────────────────────────────────────────────────────

    TimeoutError: page.waitForSelector: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('text=Magic link sent! Please check your email') to be visible


       at auth-helpers.ts:197

      195 |
      196 |   // Wait for the magic link sent message
    > 197 |   await page.waitForSelector('text=Magic link sent! Please check your email', { timeout: 10000 })
          |              ^
      198 |
      199 |   // Now simulate clicking the magic link by navigating to the magic link page
      200 |   await page.goto(`/auth/magic-link#${token}`)
        at signInSuperAdmin (/home/<USER>/work/workwell/workwell/src/__tests__/e2e/auth-helpers.ts:197:14)
        at /home/<USER>/work/workwell/workwell/src/__tests__/e2e/admin-workflows.spec.ts:195:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/admin-workflows-Admin-Work-9253e--Tests-Super-Admin-Workflow-chromium-retry1/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: test-results/admin-workflows-Admin-Work-9253e--Tests-Super-Admin-Workflow-chromium-retry1/error-context.md

    attachment #3: trace (application/zip) ─────────────────────────────────────────────────────────
    test-results/admin-workflows-Admin-Work-9253e--Tests-Super-Admin-Workflow-chromium-retry1/trace.zip
    Usage:

        npx playwright show-trace test-results/admin-workflows-Admin-Work-9253e--Tests-Super-Admin-Workflow-chromium-retry1/trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────

    Retry #2 ───────────────────────────────────────────────────────────────────────────────────────

    TimeoutError: page.waitForSelector: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('text=Magic link sent! Please check your email') to be visible


       at auth-helpers.ts:197

      195 |
      196 |   // Wait for the magic link sent message
    > 197 |   await page.waitForSelector('text=Magic link sent! Please check your email', { timeout: 10000 })
          |              ^
      198 |
      199 |   // Now simulate clicking the magic link by navigating to the magic link page
      200 |   await page.goto(`/auth/magic-link#${token}`)
        at signInSuperAdmin (/home/<USER>/work/workwell/workwell/src/__tests__/e2e/auth-helpers.ts:197:14)
        at /home/<USER>/work/workwell/workwell/src/__tests__/e2e/admin-workflows.spec.ts:195:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/admin-workflows-Admin-Work-9253e--Tests-Super-Admin-Workflow-chromium-retry2/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: test-results/admin-workflows-Admin-Work-9253e--Tests-Super-Admin-Workflow-chromium-retry2/error-context.md

  2) [chromium] › src/__tests__/e2e/user-journeys.spec.ts:945:3 › Critical User Journeys › Insights Page Comprehensive Testing 

    Test timeout of 60000ms exceeded.

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/user-journeys-Critical-Use-903c8--Page-Comprehensive-Testing-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: test-results/user-journeys-Critical-Use-903c8--Page-Comprehensive-Testing-chromium/error-context.md

    Retry #1 ───────────────────────────────────────────────────────────────────────────────────────

    Test timeout of 60000ms exceeded.

    Error: page.waitForTimeout: Test timeout of 60000ms exceeded.

       at auth-helpers.ts:287

      285 | export async function waitForPageLoad(page: Page) {
      286 |   await page.waitForLoadState('domcontentloaded')
    > 287 |   await page.waitForTimeout(2000) // Additional wait for dynamic content
          |              ^
      288 | }
      289 |
      290 | /**
        at waitForPageLoad (/home/<USER>/work/workwell/workwell/src/__tests__/e2e/auth-helpers.ts:287:14)
        at /home/<USER>/work/workwell/workwell/src/__tests__/e2e/user-journeys.spec.ts:1039:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/user-journeys-Critical-Use-903c8--Page-Comprehensive-Testing-chromium-retry1/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: test-results/user-journeys-Critical-Use-903c8--Page-Comprehensive-Testing-chromium-retry1/error-context.md

    attachment #3: trace (application/zip) ─────────────────────────────────────────────────────────
    test-results/user-journeys-Critical-Use-903c8--Page-Comprehensive-Testing-chromium-retry1/trace.zip
    Usage:

        npx playwright show-trace test-results/user-journeys-Critical-Use-903c8--Page-Comprehensive-Testing-chromium-retry1/trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────

    Retry #2 ───────────────────────────────────────────────────────────────────────────────────────

    Test timeout of 60000ms exceeded.

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/user-journeys-Critical-Use-903c8--Page-Comprehensive-Testing-chromium-retry2/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: test-results/user-journeys-Critical-Use-903c8--Page-Comprehensive-Testing-chromium-retry2/error-context.md

  3) [Mobile Chrome] › src/__tests__/e2e/admin-workflows.spec.ts:193:3 › Admin Workflow E2E Tests › Super Admin Workflow 

    TimeoutError: page.waitForSelector: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('text=Magic link sent! Please check your email') to be visible


       at auth-helpers.ts:197

      195 |
      196 |   // Wait for the magic link sent message
    > 197 |   await page.waitForSelector('text=Magic link sent! Please check your email', { timeout: 10000 })
          |              ^
      198 |
      199 |   // Now simulate clicking the magic link by navigating to the magic link page
      200 |   await page.goto(`/auth/magic-link#${token}`)
        at signInSuperAdmin (/home/<USER>/work/workwell/workwell/src/__tests__/e2e/auth-helpers.ts:197:14)
        at /home/<USER>/work/workwell/workwell/src/__tests__/e2e/admin-workflows.spec.ts:195:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/admin-workflows-Admin-Work-9253e--Tests-Super-Admin-Workflow-Mobile-Chrome/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: test-results/admin-workflows-Admin-Work-9253e--Tests-Super-Admin-Workflow-Mobile-Chrome/error-context.md

    Retry #1 ───────────────────────────────────────────────────────────────────────────────────────

    TimeoutError: page.waitForSelector: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('text=Magic link sent! Please check your email') to be visible


       at auth-helpers.ts:197

      195 |
      196 |   // Wait for the magic link sent message
    > 197 |   await page.waitForSelector('text=Magic link sent! Please check your email', { timeout: 10000 })
          |              ^
      198 |
      199 |   // Now simulate clicking the magic link by navigating to the magic link page
      200 |   await page.goto(`/auth/magic-link#${token}`)
        at signInSuperAdmin (/home/<USER>/work/workwell/workwell/src/__tests__/e2e/auth-helpers.ts:197:14)
        at /home/<USER>/work/workwell/workwell/src/__tests__/e2e/admin-workflows.spec.ts:195:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/admin-workflows-Admin-Work-9253e--Tests-Super-Admin-Workflow-Mobile-Chrome-retry1/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: test-results/admin-workflows-Admin-Work-9253e--Tests-Super-Admin-Workflow-Mobile-Chrome-retry1/error-context.md

    attachment #3: trace (application/zip) ─────────────────────────────────────────────────────────
    test-results/admin-workflows-Admin-Work-9253e--Tests-Super-Admin-Workflow-Mobile-Chrome-retry1/trace.zip
    Usage:

        npx playwright show-trace test-results/admin-workflows-Admin-Work-9253e--Tests-Super-Admin-Workflow-Mobile-Chrome-retry1/trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────

    Retry #2 ───────────────────────────────────────────────────────────────────────────────────────

    TimeoutError: page.waitForSelector: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('text=Magic link sent! Please check your email') to be visible


       at auth-helpers.ts:197

      195 |
      196 |   // Wait for the magic link sent message
    > 197 |   await page.waitForSelector('text=Magic link sent! Please check your email', { timeout: 10000 })
          |              ^
      198 |
      199 |   // Now simulate clicking the magic link by navigating to the magic link page
      200 |   await page.goto(`/auth/magic-link#${token}`)
        at signInSuperAdmin (/home/<USER>/work/workwell/workwell/src/__tests__/e2e/auth-helpers.ts:197:14)
        at /home/<USER>/work/workwell/workwell/src/__tests__/e2e/admin-workflows.spec.ts:195:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/admin-workflows-Admin-Work-9253e--Tests-Super-Admin-Workflow-Mobile-Chrome-retry2/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: test-results/admin-workflows-Admin-Work-9253e--Tests-Super-Admin-Workflow-Mobile-Chrome-retry2/error-context.md

  4) [Mobile Chrome] › src/__tests__/e2e/button-design-consistency.spec.ts:318:3 › Button Design Consistency Tests › Mobile Button Sizing 

    Error: expect(received).toBeGreaterThanOrEqual(expected)

    Expected: >= 44
    Received:    32

      334 |         if (boundingBox) {
      335 |           // WCAG 2.1 AA requires minimum 44x44px touch targets
    > 336 |           expect(boundingBox.height).toBeGreaterThanOrEqual(44)
          |                                      ^
      337 |           expect(boundingBox.width).toBeGreaterThanOrEqual(44)
      338 |         }
      339 |       }
        at /home/<USER>/work/workwell/workwell/src/__tests__/e2e/button-design-consistency.spec.ts:336:38

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/button-design-consistency--87b26--Tests-Mobile-Button-Sizing-Mobile-Chrome/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: test-results/button-design-consistency--87b26--Tests-Mobile-Button-Sizing-Mobile-Chrome/error-context.md

    Retry #1 ───────────────────────────────────────────────────────────────────────────────────────

    Error: expect(received).toBeGreaterThanOrEqual(expected)

    Expected: >= 44
    Received:    32

      334 |         if (boundingBox) {
      335 |           // WCAG 2.1 AA requires minimum 44x44px touch targets
    > 336 |           expect(boundingBox.height).toBeGreaterThanOrEqual(44)
          |                                      ^
      337 |           expect(boundingBox.width).toBeGreaterThanOrEqual(44)
      338 |         }
      339 |       }
        at /home/<USER>/work/workwell/workwell/src/__tests__/e2e/button-design-consistency.spec.ts:336:38

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/button-design-consistency--87b26--Tests-Mobile-Button-Sizing-Mobile-Chrome-retry1/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: test-results/button-design-consistency--87b26--Tests-Mobile-Button-Sizing-Mobile-Chrome-retry1/error-context.md

    attachment #3: trace (application/zip) ─────────────────────────────────────────────────────────
    test-results/button-design-consistency--87b26--Tests-Mobile-Button-Sizing-Mobile-Chrome-retry1/trace.zip
    Usage:

        npx playwright show-trace test-results/button-design-consistency--87b26--Tests-Mobile-Button-Sizing-Mobile-Chrome-retry1/trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────

    Retry #2 ───────────────────────────────────────────────────────────────────────────────────────

    Error: expect(received).toBeGreaterThanOrEqual(expected)

    Expected: >= 44
    Received:    32

      334 |         if (boundingBox) {
      335 |           // WCAG 2.1 AA requires minimum 44x44px touch targets
    > 336 |           expect(boundingBox.height).toBeGreaterThanOrEqual(44)
          |                                      ^
      337 |           expect(boundingBox.width).toBeGreaterThanOrEqual(44)
      338 |         }
      339 |       }
        at /home/<USER>/work/workwell/workwell/src/__tests__/e2e/button-design-consistency.spec.ts:336:38

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/button-design-consistency--87b26--Tests-Mobile-Button-Sizing-Mobile-Chrome-retry2/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: test-results/button-design-consistency--87b26--Tests-Mobile-Button-Sizing-Mobile-Chrome-retry2/error-context.md

  5) [Mobile Chrome] › src/__tests__/e2e/user-journeys.spec.ts:821:3 › Critical User Journeys › Mobile User Journey 

    Error: expect.toBeVisible: Error: strict mode violation: locator('text=BenefitLens') resolved to 3 elements:
        1) <span class="text-lg sm:text-xl font-bold text-gray-900">BenefitLens</span> aka getByRole('banner').getByRole('link', { name: '✓ BenefitLens' })
        2) <span class="text-lg font-bold text-gray-900">BenefitLens</span> aka getByRole('contentinfo').getByRole('link', { name: '✓ BenefitLens' })
        3) <div class="text-sm text-gray-500">…</div> aka getByText('© 2025 BenefitLens. All')

    Call log:
      - Expect "toBeVisible" with timeout 15000ms
      - waiting for locator('text=BenefitLens')


      827 |
      828 |     // Should see homepage content
    > 829 |     await expect(page.locator('text=BenefitLens')).toBeVisible()
          |                                                    ^
      830 |
      831 |     // 2. Test mobile navigation - try to find navigation elements
      832 |     const navElements = [
        at /home/<USER>/work/workwell/workwell/src/__tests__/e2e/user-journeys.spec.ts:829:52

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/user-journeys-Critical-User-Journeys-Mobile-User-Journey-Mobile-Chrome/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: test-results/user-journeys-Critical-User-Journeys-Mobile-User-Journey-Mobile-Chrome/error-context.md

    Retry #1 ───────────────────────────────────────────────────────────────────────────────────────

    Error: expect.toBeVisible: Error: strict mode violation: locator('text=BenefitLens') resolved to 3 elements:
        1) <span class="text-lg sm:text-xl font-bold text-gray-900">BenefitLens</span> aka getByRole('banner').getByRole('link', { name: '✓ BenefitLens' })
        2) <span class="text-lg font-bold text-gray-900">BenefitLens</span> aka getByRole('contentinfo').getByRole('link', { name: '✓ BenefitLens' })
        3) <div class="text-sm text-gray-500">…</div> aka getByText('© 2025 BenefitLens. All')

    Call log:
      - Expect "toBeVisible" with timeout 15000ms
      - waiting for locator('text=BenefitLens')


      827 |
      828 |     // Should see homepage content
    > 829 |     await expect(page.locator('text=BenefitLens')).toBeVisible()
          |                                                    ^
      830 |
      831 |     // 2. Test mobile navigation - try to find navigation elements
      832 |     const navElements = [
        at /home/<USER>/work/workwell/workwell/src/__tests__/e2e/user-journeys.spec.ts:829:52

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/user-journeys-Critical-User-Journeys-Mobile-User-Journey-Mobile-Chrome-retry1/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: test-results/user-journeys-Critical-User-Journeys-Mobile-User-Journey-Mobile-Chrome-retry1/error-context.md

    attachment #3: trace (application/zip) ─────────────────────────────────────────────────────────
    test-results/user-journeys-Critical-User-Journeys-Mobile-User-Journey-Mobile-Chrome-retry1/trace.zip
    Usage:

        npx playwright show-trace test-results/user-journeys-Critical-User-Journeys-Mobile-User-Journey-Mobile-Chrome-retry1/trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────

    Retry #2 ───────────────────────────────────────────────────────────────────────────────────────

    Error: expect.toBeVisible: Error: strict mode violation: locator('text=BenefitLens') resolved to 3 elements:
        1) <span class="text-lg sm:text-xl font-bold text-gray-900">BenefitLens</span> aka getByRole('banner').getByRole('link', { name: '✓ BenefitLens' })
        2) <span class="text-lg font-bold text-gray-900">BenefitLens</span> aka getByRole('contentinfo').getByRole('link', { name: '✓ BenefitLens' })
        3) <div class="text-sm text-gray-500">…</div> aka getByText('© 2025 BenefitLens. All')

    Call log:
      - Expect "toBeVisible" with timeout 15000ms
      - waiting for locator('text=BenefitLens')


      827 |
      828 |     // Should see homepage content
    > 829 |     await expect(page.locator('text=BenefitLens')).toBeVisible()
          |                                                    ^
      830 |
      831 |     // 2. Test mobile navigation - try to find navigation elements
      832 |     const navElements = [
        at /home/<USER>/work/workwell/workwell/src/__tests__/e2e/user-journeys.spec.ts:829:52

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/user-journeys-Critical-User-Journeys-Mobile-User-Journey-Mobile-Chrome-retry2/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: test-results/user-journeys-Critical-User-Journeys-Mobile-User-Journey-Mobile-Chrome-retry2/error-context.md

  6) [iPad Pro] › src/__tests__/e2e/admin-workflows.spec.ts:193:3 › Admin Workflow E2E Tests › Super Admin Workflow 

    TimeoutError: page.waitForSelector: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('text=Magic link sent! Please check your email') to be visible


       at auth-helpers.ts:197

      195 |
      196 |   // Wait for the magic link sent message
    > 197 |   await page.waitForSelector('text=Magic link sent! Please check your email', { timeout: 10000 })
          |              ^
      198 |
      199 |   // Now simulate clicking the magic link by navigating to the magic link page
      200 |   await page.goto(`/auth/magic-link#${token}`)
        at signInSuperAdmin (/home/<USER>/work/workwell/workwell/src/__tests__/e2e/auth-helpers.ts:197:14)
        at /home/<USER>/work/workwell/workwell/src/__tests__/e2e/admin-workflows.spec.ts:195:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/admin-workflows-Admin-Work-9253e--Tests-Super-Admin-Workflow-iPad-Pro/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: test-results/admin-workflows-Admin-Work-9253e--Tests-Super-Admin-Workflow-iPad-Pro/error-context.md

    Retry #1 ───────────────────────────────────────────────────────────────────────────────────────

    TimeoutError: page.waitForSelector: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('text=Magic link sent! Please check your email') to be visible


       at auth-helpers.ts:197

      195 |
      196 |   // Wait for the magic link sent message
    > 197 |   await page.waitForSelector('text=Magic link sent! Please check your email', { timeout: 10000 })
          |              ^
      198 |
      199 |   // Now simulate clicking the magic link by navigating to the magic link page
      200 |   await page.goto(`/auth/magic-link#${token}`)
        at signInSuperAdmin (/home/<USER>/work/workwell/workwell/src/__tests__/e2e/auth-helpers.ts:197:14)
        at /home/<USER>/work/workwell/workwell/src/__tests__/e2e/admin-workflows.spec.ts:195:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/admin-workflows-Admin-Work-9253e--Tests-Super-Admin-Workflow-iPad-Pro-retry1/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: test-results/admin-workflows-Admin-Work-9253e--Tests-Super-Admin-Workflow-iPad-Pro-retry1/error-context.md

    attachment #3: trace (application/zip) ─────────────────────────────────────────────────────────
    test-results/admin-workflows-Admin-Work-9253e--Tests-Super-Admin-Workflow-iPad-Pro-retry1/trace.zip
    Usage:

        npx playwright show-trace test-results/admin-workflows-Admin-Work-9253e--Tests-Super-Admin-Workflow-iPad-Pro-retry1/trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────

    Retry #2 ───────────────────────────────────────────────────────────────────────────────────────

    TimeoutError: page.waitForSelector: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('text=Magic link sent! Please check your email') to be visible


       at auth-helpers.ts:197

      195 |
      196 |   // Wait for the magic link sent message
    > 197 |   await page.waitForSelector('text=Magic link sent! Please check your email', { timeout: 10000 })
          |              ^
      198 |
      199 |   // Now simulate clicking the magic link by navigating to the magic link page
      200 |   await page.goto(`/auth/magic-link#${token}`)
        at signInSuperAdmin (/home/<USER>/work/workwell/workwell/src/__tests__/e2e/auth-helpers.ts:197:14)
        at /home/<USER>/work/workwell/workwell/src/__tests__/e2e/admin-workflows.spec.ts:195:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/admin-workflows-Admin-Work-9253e--Tests-Super-Admin-Workflow-iPad-Pro-retry2/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: test-results/admin-workflows-Admin-Work-9253e--Tests-Super-Admin-Workflow-iPad-Pro-retry2/error-context.md

  7) [iPad Pro] › src/__tests__/e2e/user-journeys.spec.ts:75:3 › Critical User Journeys › User Benefit Management Journey 

    Error: expect(received).toBeGreaterThan(expected)

    Expected: > 0
    Received:   0

      152 |     }
      153 |
    > 154 |     expect(selectedCount).toBeGreaterThan(0) // Ensure we selected at least one benefit
          |                           ^
      155 |     console.log(`✅ Selected ${selectedCount} benefits for testing`)
      156 |
      157 |     // Proceed to review step
        at /home/<USER>/work/workwell/workwell/src/__tests__/e2e/user-journeys.spec.ts:154:27

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/user-journeys-Critical-Use-c1e07--Benefit-Management-Journey-iPad-Pro/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: test-results/user-journeys-Critical-Use-c1e07--Benefit-Management-Journey-iPad-Pro/error-context.md

    Retry #1 ───────────────────────────────────────────────────────────────────────────────────────

    Error: expect(received).toBeGreaterThan(expected)

    Expected: > 0
    Received:   0

      152 |     }
      153 |
    > 154 |     expect(selectedCount).toBeGreaterThan(0) // Ensure we selected at least one benefit
          |                           ^
      155 |     console.log(`✅ Selected ${selectedCount} benefits for testing`)
      156 |
      157 |     // Proceed to review step
        at /home/<USER>/work/workwell/workwell/src/__tests__/e2e/user-journeys.spec.ts:154:27

    attachment #1: screenshot (image/png) ──────────────��───────────────────────────────────────────
    test-results/user-journeys-Critical-Use-c1e07--Benefit-Management-Journey-iPad-Pro-retry1/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: test-results/user-journeys-Critical-Use-c1e07--Benefit-Management-Journey-iPad-Pro-retry1/error-context.md

    attachment #3: trace (application/zip) ─────────────────────────────────────────────────────────
    test-results/user-journeys-Critical-Use-c1e07--Benefit-Management-Journey-iPad-Pro-retry1/trace.zip
    Usage:

        npx playwright show-trace test-results/user-journeys-Critical-Use-c1e07--Benefit-Management-Journey-iPad-Pro-retry1/trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────

    Retry #2 ───────────────────────────────────────────────────────────────────────────────────────

    Error: expect(received).toBeGreaterThan(expected)

    Expected: > 0
    Received:   0

      152 |     }
      153 |
    > 154 |     expect(selectedCount).toBeGreaterThan(0) // Ensure we selected at least one benefit
          |                           ^
      155 |     console.log(`✅ Selected ${selectedCount} benefits for testing`)
      156 |
      157 |     // Proceed to review step
        at /home/<USER>/work/workwell/workwell/src/__tests__/e2e/user-journeys.spec.ts:154:27

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/user-journeys-Critical-Use-c1e07--Benefit-Management-Journey-iPad-Pro-retry2/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: test-results/user-journeys-Critical-Use-c1e07--Benefit-Management-Journey-iPad-Pro-retry2/error-context.md

  8) [iPad Pro] › src/__tests__/e2e/user-journeys.spec.ts:945:3 › Critical User Journeys › Insights Page Comprehensive Testing 

    Test timeout of 60000ms exceeded.

    Error: page.waitForTimeout: Test timeout of 60000ms exceeded.

       at auth-helpers.ts:287

      285 | export async function waitForPageLoad(page: Page) {
      286 |   await page.waitForLoadState('domcontentloaded')
    > 287 |   await page.waitForTimeout(2000) // Additional wait for dynamic content
          |              ^
      288 | }
      289 |
      290 | /**
        at waitForPageLoad (/home/<USER>/work/workwell/workwell/src/__tests__/e2e/auth-helpers.ts:287:14)
        at /home/<USER>/work/workwell/workwell/src/__tests__/e2e/user-journeys.spec.ts:978:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/user-journeys-Critical-Use-903c8--Page-Comprehensive-Testing-iPad-Pro/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: test-results/user-journeys-Critical-Use-903c8--Page-Comprehensive-Testing-iPad-Pro/error-context.md

    Retry #1 ───────────────────────────────────────────────────────────────────────────────────────

    Test timeout of 60000ms exceeded.

    Error: page.waitForTimeout: Test timeout of 60000ms exceeded.

       at auth-helpers.ts:287

      285 | export async function waitForPageLoad(page: Page) {
      286 |   await page.waitForLoadState('domcontentloaded')
    > 287 |   await page.waitForTimeout(2000) // Additional wait for dynamic content
          |              ^
      288 | }
      289 |
      290 | /**
        at waitForPageLoad (/home/<USER>/work/workwell/workwell/src/__tests__/e2e/auth-helpers.ts:287:14)
        at /home/<USER>/work/workwell/workwell/src/__tests__/e2e/user-journeys.spec.ts:1039:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/user-journeys-Critical-Use-903c8--Page-Comprehensive-Testing-iPad-Pro-retry1/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: test-results/user-journeys-Critical-Use-903c8--Page-Comprehensive-Testing-iPad-Pro-retry1/error-context.md

    attachment #3: trace (application/zip) ─────────────────────────────────────────────────────────
    test-results/user-journeys-Critical-Use-903c8--Page-Comprehensive-Testing-iPad-Pro-retry1/trace.zip
    Usage:

        npx playwright show-trace test-results/user-journeys-Critical-Use-903c8--Page-Comprehensive-Testing-iPad-Pro-retry1/trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────

    Retry #2 ───────────────────────────────────────────────────────────────────────────────────────

    Test timeout of 60000ms exceeded.

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/user-journeys-Critical-Use-903c8--Page-Comprehensive-Testing-iPad-Pro-retry2/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: test-results/user-journeys-Critical-Use-903c8--Page-Comprehensive-Testing-iPad-Pro-retry2/error-context.md

  9) [chromium] › src/__tests__/e2e/essential-tests.spec.ts:84:3 › Essential E2E Tests › Public Homepage and Search 

    Test timeout of 60000ms exceeded.

    Error: page.waitForTimeout: Test timeout of 60000ms exceeded.

       at auth-helpers.ts:287

      285 | export async function waitForPageLoad(page: Page) {
      286 |   await page.waitForLoadState('domcontentloaded')
    > 287 |   await page.waitForTimeout(2000) // Additional wait for dynamic content
          |              ^
      288 | }
      289 |
      290 | /**
        at waitForPageLoad (/home/<USER>/work/workwell/workwell/src/__tests__/e2e/auth-helpers.ts:287:14)
        at /home/<USER>/work/workwell/workwell/src/__tests__/e2e/essential-tests.spec.ts:113:7

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/essential-tests-Essential--cdf50--Public-Homepage-and-Search-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: test-results/essential-tests-Essential--cdf50--Public-Homepage-and-Search-chromium/error-context.md

  10) [Mobile Chrome] › src/__tests__/e2e/admin-company-benefits.spec.ts:10:3 › Admin Company Benefits Tests › Company benefits filtering should work correctly 

    Error: expect(received).toBeGreaterThan(expected)

    Expected: > 0
    Received:   0

      23 |     const benefitRows = page.locator('tbody tr')
      24 |     const initialCount = await benefitRows.count()
    > 25 |     expect(initialCount).toBeGreaterThan(0)
         |                          ^
      26 |     
      27 |     console.log(`Initial company benefits count: ${initialCount}`)
      28 |     
        at /home/<USER>/work/workwell/workwell/src/__tests__/e2e/admin-company-benefits.spec.ts:25:26

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/admin-company-benefits-Adm-9657e-ering-should-work-correctly-Mobile-Chrome/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: test-results/admin-company-benefits-Adm-9657e-ering-should-work-correctly-Mobile-Chrome/error-context.md

  11) [Mobile Chrome] › src/__tests__/e2e/device-specific-tests.spec.ts:189:3 › Device-Specific Tests › Touch vs Mouse Interactions 

    Error: expect(received).toBe(expected) // Object.is equality

    Expected: true
    Received: false

      223 |     const hasFoundText = await foundText.isVisible({ timeout: 5000 }).catch(() => false)
      224 |
    > 225 |     expect(hasCompanyCards || hasFoundText).toBe(true)
          |                                             ^
      226 |     
      227 |     console.log('✅ Input method compatibility verified')
      228 |   })
        at /home/<USER>/work/workwell/workwell/src/__tests__/e2e/device-specific-tests.spec.ts:225:45

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/device-specific-tests-Devi-8801e-Touch-vs-Mouse-Interactions-Mobile-Chrome/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: test-results/device-specific-tests-Devi-8801e-Touch-vs-Mouse-Interactions-Mobile-Chrome/error-context.md

  12) [Mobile Chrome] › src/__tests__/e2e/essential-tests.spec.ts:64:3 › Essential E2E Tests › User Authentication and Dashboard 

    Test timeout of 60000ms exceeded.

    Error: page.waitForTimeout: Test timeout of 60000ms exceeded.

       at auth-helpers.ts:287

      285 | export async function waitForPageLoad(page: Page) {
      286 |   await page.waitForLoadState('domcontentloaded')
    > 287 |   await page.waitForTimeout(2000) // Additional wait for dynamic content
          |              ^
      288 | }
      289 |
      290 | /**
        at waitForPageLoad (/home/<USER>/work/workwell/workwell/src/__tests__/e2e/auth-helpers.ts:287:14)
        at /home/<USER>/work/workwell/workwell/src/__tests__/e2e/essential-tests.spec.ts:71:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/essential-tests-Essential--59f1f-uthentication-and-Dashboard-Mobile-Chrome/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: test-results/essential-tests-Essential--59f1f-uthentication-and-Dashboard-Mobile-Chrome/error-context.md

  13) [Mobile Chrome] › src/__tests__/e2e/essential-tests.spec.ts:84:3 › Essential E2E Tests › Public Homepage and Search 

    Test timeout of 60000ms exceeded.

    Error: expect(locator).toBeVisible() failed

    Locator:  locator('.company-card, [data-testid="company-card"], .search-result').first()
    Expected: visible
    Received: <element(s) not found>

    Call log:
      - Expect "toBeVisible" with timeout 10000ms
      - waiting for locator('.company-card, [data-testid="company-card"], .search-result').first()


      102 |
      103 |       // Check that at least one result is visible (using first() to avoid strict mode violation)
    > 104 |       await expect(results.first()).toBeVisible({ timeout: 10000 })
          |                                     ^
      105 |       console.log('✅ Search functionality working')
      106 |     }
      107 |
        at /home/<USER>/work/workwell/workwell/src/__tests__/e2e/essential-tests.spec.ts:104:37

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/essential-tests-Essential--cdf50--Public-Homepage-and-Search-Mobile-Chrome/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: test-results/essential-tests-Essential--cdf50--Public-Homepage-and-Search-Mobile-Chrome/error-context.md

  14) [Mobile Chrome] › src/__tests__/e2e/user-journeys.spec.ts:75:3 › Critical User Journeys › User Benefit Management Journey 

    Error: expect(received).toBeGreaterThan(expected)

    Expected: > 0
    Received:   0

      152 |     }
      153 |
    > 154 |     expect(selectedCount).toBeGreaterThan(0) // Ensure we selected at least one benefit
          |                           ^
      155 |     console.log(`✅ Selected ${selectedCount} benefits for testing`)
      156 |
      157 |     // Proceed to review step
        at /home/<USER>/work/workwell/workwell/src/__tests__/e2e/user-journeys.spec.ts:154:27

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/user-journeys-Critical-Use-c1e07--Benefit-Management-Journey-Mobile-Chrome/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: test-results/user-journeys-Critical-Use-c1e07--Benefit-Management-Journey-Mobile-Chrome/error-context.md

  15) [Mobile Chrome] › src/__tests__/e2e/user-journeys.spec.ts:222:3 › Critical User Journeys › User Benefit Ranking Journey 

    Test timeout of 60000ms exceeded.

    Error: page.waitForTimeout: Test timeout of 60000ms exceeded.

      457 |
      458 |     // Wait for analytics data to load
    > 459 |     await page.waitForTimeout(3000)
          |                ^
      460 |
      461 |     // Check if analytics API returns data that includes our specific rankings
      462 |     const analyticsResponse = await page.request.get('/api/analytics/benefit-rankings?period=7d')
        at /home/<USER>/work/workwell/workwell/src/__tests__/e2e/user-journeys.spec.ts:459:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/user-journeys-Critical-Use-5ec55-ser-Benefit-Ranking-Journey-Mobile-Chrome/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: test-results/user-journeys-Critical-Use-5ec55-ser-Benefit-Ranking-Journey-Mobile-Chrome/error-context.md

  16) [Mobile Chrome] › src/__tests__/e2e/user-journeys.spec.ts:945:3 › Critical User Journeys › Insights Page Comprehensive Testing 

    Test timeout of 60000ms exceeded.

    Error: page.waitForTimeout: Test timeout of 60000ms exceeded.

       at auth-helpers.ts:287

      285 | export async function waitForPageLoad(page: Page) {
      286 |   await page.waitForLoadState('domcontentloaded')
    > 287 |   await page.waitForTimeout(2000) // Additional wait for dynamic content
          |              ^
      288 | }
      289 |
      290 | /**
        at waitForPageLoad (/home/<USER>/work/workwell/workwell/src/__tests__/e2e/auth-helpers.ts:287:14)
        at /home/<USER>/work/workwell/workwell/src/__tests__/e2e/user-journeys.spec.ts:1039:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/user-journeys-Critical-Use-903c8--Page-Comprehensive-Testing-Mobile-Chrome/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: test-results/user-journeys-Critical-Use-903c8--Page-Comprehensive-Testing-Mobile-Chrome/error-context.md

  17) [iPad Pro] › src/__tests__/e2e/admin-company-benefits.spec.ts:10:3 › Admin Company Benefits Tests › Company benefits filtering should work correctly 

    Error: expect(received).toBeGreaterThan(expected)

    Expected: > 0
    Received:   0

      23 |     const benefitRows = page.locator('tbody tr')
      24 |     const initialCount = await benefitRows.count()
    > 25 |     expect(initialCount).toBeGreaterThan(0)
         |                          ^
      26 |     
      27 |     console.log(`Initial company benefits count: ${initialCount}`)
      28 |     
        at /home/<USER>/work/workwell/workwell/src/__tests__/e2e/admin-company-benefits.spec.ts:25:26

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/admin-company-benefits-Adm-9657e-ering-should-work-correctly-iPad-Pro/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: test-results/admin-company-benefits-Adm-9657e-ering-should-work-correctly-iPad-Pro/error-context.md

  18) [iPad Pro] › src/__tests__/e2e/device-specific-tests.spec.ts:189:3 › Device-Specific Tests › Touch vs Mouse Interactions 

    Error: expect(received).toBe(expected) // Object.is equality

    Expected: true
    Received: false

      223 |     const hasFoundText = await foundText.isVisible({ timeout: 5000 }).catch(() => false)
      224 |
    > 225 |     expect(hasCompanyCards || hasFoundText).toBe(true)
          |                                             ^
      226 |     
      227 |     console.log('✅ Input method compatibility verified')
      228 |   })
        at /home/<USER>/work/workwell/workwell/src/__tests__/e2e/device-specific-tests.spec.ts:225:45

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/device-specific-tests-Devi-8801e-Touch-vs-Mouse-Interactions-iPad-Pro/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: test-results/device-specific-tests-Devi-8801e-Touch-vs-Mouse-Interactions-iPad-Pro/error-context.md

  19) [iPad Pro] › src/__tests__/e2e/user-journeys.spec.ts:222:3 › Critical User Journeys › User Benefit Ranking Journey 

    Test timeout of 60000ms exceeded.

    Error: apiRequestContext.get: Target page, context or browser has been closed
    Call log:
      - → GET http://localhost:3000/api/analytics/benefit-rankings?period=7d
        - user-agent: Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) HeadlessChrome/140.0.7339.16 Safari/537.36
        - accept: */*
        - accept-encoding: gzip,deflate,br
        - cookie: session_token=6da60a13-b36c-4c5e-b495-c96be75445ac


      460 |
      461 |     // Check if analytics API returns data that includes our specific rankings
    > 462 |     const analyticsResponse = await page.request.get('/api/analytics/benefit-rankings?period=7d')
          |                                                  ^
      463 |     if (analyticsResponse.ok()) {
      464 |       const analyticsData = await analyticsResponse.json()
      465 |       console.log('Analytics data summary:', {
        at /home/<USER>/work/workwell/workwell/src/__tests__/e2e/user-journeys.spec.ts:462:50

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/user-journeys-Critical-Use-5ec55-ser-Benefit-Ranking-Journey-iPad-Pro/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: test-results/user-journeys-Critical-Use-5ec55-ser-Benefit-Ranking-Journey-iPad-Pro/error-context.md

  8 failed
    [chromium] › src/__tests__/e2e/admin-workflows.spec.ts:193:3 › Admin Workflow E2E Tests › Super Admin Workflow 
    [chromium] › src/__tests__/e2e/user-journeys.spec.ts:945:3 › Critical User Journeys › Insights Page Comprehensive Testing 
    [Mobile Chrome] › src/__tests__/e2e/admin-workflows.spec.ts:193:3 › Admin Workflow E2E Tests › Super Admin Workflow 
    [Mobile Chrome] › src/__tests__/e2e/button-design-consistency.spec.ts:318:3 › Button Design Consistency Tests › Mobile Button Sizing 
    [Mobile Chrome] › src/__tests__/e2e/user-journeys.spec.ts:821:3 › Critical User Journeys › Mobile User Journey 
    [iPad Pro] › src/__tests__/e2e/admin-workflows.spec.ts:193:3 › Admin Workflow E2E Tests › Super Admin Workflow 
    [iPad Pro] › src/__tests__/e2e/user-journeys.spec.ts:75:3 › Critical User Journeys › User Benefit Management Journey 
    [iPad Pro] › src/__tests__/e2e/user-journeys.spec.ts:945:3 › Critical User Journeys › Insights Page Comprehensive Testing 
  11 flaky
    [chromium] › src/__tests__/e2e/essential-tests.spec.ts:84:3 › Essential E2E Tests › Public Homepage and Search 
    [Mobile Chrome] › src/__tests__/e2e/admin-company-benefits.spec.ts:10:3 › Admin Company Benefits Tests › Company benefits filtering should work correctly 
    [Mobile Chrome] › src/__tests__/e2e/device-specific-tests.spec.ts:189:3 › Device-Specific Tests › Touch vs Mouse Interactions 
    [Mobile Chrome] › src/__tests__/e2e/essential-tests.spec.ts:64:3 › Essential E2E Tests › User Authentication and Dashboard 
    [Mobile Chrome] › src/__tests__/e2e/essential-tests.spec.ts:84:3 › Essential E2E Tests › Public Homepage and Search 
    [Mobile Chrome] › src/__tests__/e2e/user-journeys.spec.ts:75:3 › Critical User Journeys › User Benefit Management Journey 
    [Mobile Chrome] › src/__tests__/e2e/user-journeys.spec.ts:222:3 › Critical User Journeys › User Benefit Ranking Journey 
    [Mobile Chrome] › src/__tests__/e2e/user-journeys.spec.ts:945:3 › Critical User Journeys › Insights Page Comprehensive Testing 
    [iPad Pro] › src/__tests__/e2e/admin-company-benefits.spec.ts:10:3 › Admin Company Benefits Tests › Company benefits filtering should work correctly 
    [iPad Pro] › src/__tests__/e2e/device-specific-tests.spec.ts:189:3 › Device-Specific Tests › Touch vs Mouse Interactions 
    [iPad Pro] › src/__tests__/e2e/user-journeys.spec.ts:222:3 › Critical User Journeys › User Benefit Ranking Journey 